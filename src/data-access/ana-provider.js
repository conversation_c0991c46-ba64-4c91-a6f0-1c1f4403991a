import { ANA } from "client/api";
import apiBase from "./api-base";
import { client, dataPath } from "client/request";
import { combineUrlParams } from "utils";

export default {
  ...apiBase.init({ API: ANA }),
  searchDongBoChungTu: ({ page = 0, sort, size = 10, ...payload } = {}) => {
    return new Promise((resolve, reject) => {
      client
        .get(
          combineUrlParams(`${dataPath}${ANA}/kho-phieu-nhap-xuat-chi-tiet`, {
            page: page + "",
            sort,
            size,
            ...payload,
          })
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch(reject);
    });
  },
  dongBoChungTu: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(`${dataPath}${ANA}/kho-phieu-nhap-xuat-chi-tiet/dong-bo`, payload)
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
  huyDongBoChungTu: (payload) => {
    return new Promise((resolve, reject) => {
      client
        .post(
          `${dataPath}${ANA}/kho-phieu-nhap-xuat-chi-tiet/huy-dong-bo`,
          payload
        )
        .then((s) => {
          if (s?.data?.code === 0) resolve(s?.data);
          else reject(s?.data);
        })
        .catch((e) => {
          reject(e);
        });
    });
  },
};

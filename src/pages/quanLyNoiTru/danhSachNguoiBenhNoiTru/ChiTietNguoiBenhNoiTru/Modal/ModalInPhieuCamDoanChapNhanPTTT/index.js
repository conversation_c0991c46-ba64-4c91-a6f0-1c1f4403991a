import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
  useRef,
} from "react";
import { Main } from "./styled";
import { DatePicker, Button, ModalTemplate, Select } from "components";
import { useTranslation } from "react-i18next";
import { useStore } from "hooks";
import moment from "moment";
import { isEmpty } from "lodash";
import { SVG } from "assets";
import TableChonPhieu from "../../ToDieuTri/modals/TableChonPhieu";
import ModalConfirmChonPhieu from "pages/quanLyNoiTru/danhSachNguoiBenhNoiTru/ChiTietNguoiBenhNoiTru/Modal/ModalConfirmChonPhieu";

const ModalInPhieuCamDoanChapNhanPTTT = (props, ref) => {
  const { t } = useTranslation();
  const refModal = useRef(null);
  const refCallback = useRef(null);
  const refModalConfirmChonPhieu = useRef(null);

  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);

  //state
  const [state, _setState] = useState({
    show: false,
    checkValidate: false,
    thoiGianThucHien: moment(),
    khoaChiDinhId: null,
    selectedPhieu: null,
    dsSoPhieu: [],
  });

  const setState = (data) => {
    _setState({
      ...state,
      ...data,
    });
  };

  useImperativeHandle(ref, () => ({
    show: (data = {}, onOk) => {
      const _dsSoPhieu = (data.dsSoPhieu || [])
        .filter((item) => !isEmpty(item.soPhieu))
        .map((item) => ({
          ...item,
          id: item.soPhieu,
        }))
        .sort(
          (a, b) =>
            new Date(b.thoiGianThucHien).getTime() -
            new Date(a.thoiGianThucHien).getTime()
        );

      setState({
        show: true,
        khoaChiDinhId: data?.khoaChiDinhId || null,
        dsSoPhieu: _dsSoPhieu,
        tenPhieu: data.ten,
        maPhieu: data.ma,
      });

      refCallback.current = onOk;
    },
  }));

  //function
  const onClose = () => {
    setState({
      show: false,
      checkValidate: false,
      thoiGianThucHien: moment(),
      khoaChiDinhId: null,
    });
  };

  const onCheckValidate = () => {
    setState({ checkValidate: true });
    if (!state.thoiGianThucHien || !state.khoaChiDinhId) {
      return false;
    }

    return true;
  };

  const onSelectPhieuDaIn = (payload = {}) => {
    const data = {
      id: payload.id,
      khoaChiDinhId: payload.khoaChiDinhId,
      thoiGianThucHien: payload.thoiGianThucHien
        ? moment(payload.thoiGianThucHien).format("YYYY-MM-DD HH:mm")
        : null,
    };

    refCallback.current && refCallback.current(data);
    setState({
      show: false,
      selectedPhieu: null,
      dsSoPhieu: [],
      selectedRowKeys: null,
    });
  };

  const onSave = () => {
    if (state.selectedPhieu) {
      onSelectPhieuDaIn(state.selectedPhieu);
    } else {
      if (!onCheckValidate()) {
        return;
      }

      const { thoiGianThucHien, khoaChiDinhId } = state;

      //kiểm tra trùng từ ngày trong ds số phiếu
      const _ngayThucHien = moment(thoiGianThucHien).format("DD/MM/YYYY HH:mm");
      const _checkSoPhieu = (state.dsSoPhieu || []).filter(
        (item) =>
          moment(item.thoiGianThucHien).format("DD/MM/YYYY HH:mm") ==
            _ngayThucHien && item.khoaChiDinhId == khoaChiDinhId
      );

      if (_checkSoPhieu.length > 0) {
        refModalConfirmChonPhieu.current &&
          refModalConfirmChonPhieu.current.show(
            {
              dsSoPhieu: _checkSoPhieu,
              tuNgay: _ngayThucHien,
              showThoiGianIn: true,
            },
            () => {
              refCallback.current({
                thoiGianThucHien: thoiGianThucHien.format("YYYY-MM-DD HH:mm"),
                khoaChiDinhId,
              });
              onClose();
            },
            onSelectPhieuDaIn
          );
      } else {
        refCallback.current({
          thoiGianThucHien: thoiGianThucHien.format("YYYY-MM-DD HH:mm"),
          khoaChiDinhId,
        });
        onClose();
      }
    }
  };

  useEffect(() => {
    if (state.show) {
      refModal.current && refModal.current.show();
    } else {
      refModal.current && refModal.current.hide();
    }
  }, [state.show]);

  const onChange = (key) => (e) => {
    setState({
      [key]: e || null,
    });
  };

  return (
    <ModalTemplate
      ref={refModal}
      width={850}
      onCancel={onClose}
      title={state.tenPhieu}
      actionLeft={<Button.QuayLai onClick={onClose} />}
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onSave}
          rightIcon={<SVG.IcPrint />}
        >
          {t("common.in")}
        </Button>
      }
    >
      <Main>
        <div className="form">
          <div className="form-item">
            <div className="label">
              <span>{t("danhMuc.khoaChiDinh")}:</span>
              {!state.selectedPhieu && <span className="error">*</span>}
            </div>
            <div className="content">
              <Select
                value={state.khoaChiDinhId}
                data={listKhoaTheoTaiKhoan || []}
                placeholder={t("danhMuc.chonKhoaChiDinh")}
                onChange={onChange("khoaChiDinhId")}
              />
            </div>
          </div>

          {state?.checkValidate && !state.khoaChiDinhId && (
            <span className="error">{t("danhMuc.vuiLongChonKhoaChiDinh")}</span>
          )}

          <div className="form-item">
            <div className="label">
              <span>{t("quanLyNoiTru.thoiGianIn")}:</span>
              {!state.selectedPhieu && <span className="error">*</span>}
            </div>
            <div className="content">
              <DatePicker
                showTime
                value={
                  state.thoiGianThucHien ? moment(state.thoiGianThucHien) : null
                }
                format="DD-MM-YYYY HH:mm"
                placeholder={t("quanLyNoiTru.chonThoiGianIn")}
                onChange={onChange("thoiGianThucHien")}
              ></DatePicker>
            </div>
          </div>
          {state?.checkValidate && !state.thoiGianThucHien && (
            <span className="error">
              {t("quanLyNoiTru.vuiLongChonThoiGianIn")}
            </span>
          )}
        </div>

        <div className="ds-phieu-da-in">
          <TableChonPhieu
            dsSoPhieu={state?.dsSoPhieu}
            setParentState={setState}
            selectedRowKeys={state?.selectedRowKeys}
            showThoiGianIn={true}
          />
        </div>

        <ModalConfirmChonPhieu ref={refModalConfirmChonPhieu} />
      </Main>
    </ModalTemplate>
  );
};

export default forwardRef(ModalInPhieuCamDoanChapNhanPTTT);

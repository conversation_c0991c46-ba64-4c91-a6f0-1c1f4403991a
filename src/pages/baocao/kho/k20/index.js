import { Col, Row } from "antd";
import { Select, SelectLoadMore, Checkbox, InputTimeout } from "components";
import {
  useEnum,
  useListAll,
  useStore,
  useCache,
  useLazyKVMap,
  useQueryAll,
  useFillMaHoSo,
} from "hooks";
import moment from "moment";
import { useTranslation } from "react-i18next";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import BaseBaoCao from "pages/baocao/BaseBaoCao";
import { Main } from "./styled";
import {
  LOAI_DICH_VU,
  ENUM,
  LOAI_PHONG,
  NHOM_DON_VI_TINH,
  CACHE_KEY,
  PHAN_LOAI_DOI_TUONG_KCB,
} from "constants/index";
import { isArray } from "utils/index";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { query, select } from "redux-store/stores";
import { uniqBy } from "lodash";
import { selectMaTen } from "redux-store/selectors";
import dmDichVuKhoProvider from "data-access/categories/dm-dich-vu-kho-provider";
import { FilterThoiGian } from "pages/baocao/BaseBaoCao/components";

const K20 = () => {
  const { t } = useTranslation();
  const listKhoUser = useStore("kho.listKhoUser", []);
  const listKhoaTheoTaiKhoan = useStore("khoa.listKhoaTheoTaiKhoan", []);
  const listAllKho = useStore("kho.listAllKho", []);
  const [listLoaiDichVuKho] = useEnum(ENUM.LOAI_DICH_VU_KHO);
  const [listDoiTuong] = useEnum(ENUM.DOI_TUONG);
  const [listLoaiChiDinh] = useEnum(ENUM.LOAI_CHI_DINH);
  const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);
  const [listLoaiPhong] = useEnum(ENUM.LOAI_PHONG);
  const [listLoaiThoiGian] = useEnum(ENUM.LOAI_THOI_GIAN);
  const [listLoaiDichVu] = useEnum(ENUM.LOAI_DICH_VU);
  const refSelect = useRef(null);
  const listAllPhong = useStore("phong.listAllPhong", []);
  const [dsKhoaId, setDsKhoaId] = useState([]);
  const [listAllNhomDonViTinh] = useListAll(
    "donViTinh",
    {},
    true,
    "NhomDonViTinh"
  );
  const [listAllKhoaTheoTaiKhoan] = useListAll(
    "khoa",
    {},
    true,
    "KhoaTheoTaiKhoan"
  );

  const listLoaiThongKe = [
    {
      id: true,
      ten: t("baoCao.hoanTra"),
    },
    {
      id: false,
      ten: t("baoCao.thuong"),
    },
  ];
  const [cacheSapXepTheoPhong, setCacheSapXepTheoPhong] = useCache(
    "",
    CACHE_KEY.SAP_XEP_THEO_PHONG_K20,
    false,
    false
  );
  const [cacheTrangThaiPhieu, setCacheTrangThaiPhieu] = useCache(
    "",
    CACHE_KEY.TRANG_THAI_PHIEU_K20,
    null,
    false
  );
  const [cacheLoaiDichVu, setCacheLoaiDichVu] = useCache(
    "",
    CACHE_KEY.LOAI_DICH_VU_K20,
    "",
    false
  );
  const [khoaChiDinh, setKhoaChiDinh, loadFinish] = useCache(
    "",
    CACHE_KEY.KHOA_CHI_DINH_K20,
    null,
    false
  );
  const [cacheSapXepTheoTenNb, setCacheSapXepTheoTenNb] = useCache(
    "",
    CACHE_KEY.SAP_XEP_THEO_TEN_NB_K20,
    false,
    false
  );
  const [cacheHienThiPhongTheoKhoa, setCacheHienThiPhongTheoKhoa] = useCache(
    "",
    CACHE_KEY.HIEN_THI_PHONG_THEO_KHOA_K20,
    false,
    false
  );

  const [getPhanLoaiDoiTuongKcb] = useLazyKVMap(PHAN_LOAI_DOI_TUONG_KCB);
  const listAllPhanLoaiThuoc = useStore(
    "phanLoaiThuoc.listAllPhanLoaiThuoc",
    []
  );
  const { formatMaHoSo, testMaHoSo } = useFillMaHoSo();

  const {
    kho: { getTheoTaiKhoan, getListAllKho },
    khoa: { getKhoaTheoTaiKhoan },
    baoCaoDaIn: { getK20 },
    phong: { getListAllPhong },
    phanLoaiThuoc: { getListAllPhanLoaiThuoc },
  } = useDispatch();

  useEffect(() => {
    getKhoaTheoTaiKhoan({ active: true, page: "", size: "" });
    getListAllPhanLoaiThuoc({ page: "", size: "", active: true });
  }, []);

  const { data: listAllPhongChiDinh } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        dsLoaiPhong: listLoaiPhong
          .filter(
            (i) =>
              ![
                LOAI_PHONG.PHONG_GIUONG,
                LOAI_PHONG.PHONG_GIUONG_TU_CHON,
              ].includes(i.id)
          )
          .map((i) => i.id),
        dsKhoaId,
      },
    })
  );

  const fetchAllPhong = ({ hienThiPhongTheoKhoa, dsKhoaId }) => {
    const dsLoaiPhong = !hienThiPhongTheoKhoa
      ? [LOAI_PHONG.PHONG_GIUONG, LOAI_PHONG.PHONG_GIUONG_TU_CHON]
      : [];

    getListAllPhong({
      active: true,
      page: "",
      size: "",
      dsLoaiPhong,
      dsKhoaId,
    });
  };

  useEffect(() => {
    const dsKhoaId =
      (khoaChiDinh?.length
        ? khoaChiDinh
        : listAllKhoaTheoTaiKhoan?.map((item) => item.id)) || [];

    if (dsKhoaId.length && loadFinish) {
      fetchAllPhong({
        hienThiPhongTheoKhoa: cacheHienThiPhongTheoKhoa,
        dsKhoaId,
      });
      setDsKhoaId(dsKhoaId);
    }
  }, [
    listAllKhoaTheoTaiKhoan,
    khoaChiDinh,
    loadFinish,
    cacheHienThiPhongTheoKhoa,
  ]);

  useEffect(() => {
    if (cacheLoaiDichVu) {
      getTheoTaiKhoan({
        active: true,
        page: "",
        size: "",
        dsLoaiDichVu: cacheLoaiDichVu,
        dsCoCheDuyetPhat: [20],
      });
      getListAllKho({
        active: true,
        page: "",
        size: "",
        dsCoCheDuyetPhat: [10, 30],
        dsLoaiDichVu: cacheLoaiDichVu,
      });
    }
  }, [cacheLoaiDichVu]);

  const listTrangThai = [
    {
      id: "10",
      ten: t("common.chuaPhat"),
    },
    {
      id: "20",
      ten: t("common.daPhat"),
    },
    {
      id: "30",
      ten: t("kho.daDuyetDLS"),
    },
  ];

  const listAllNhomDonViTinhMemo = useMemo(() => {
    return {
      ONG: {
        id: JSON.stringify(
          listAllNhomDonViTinh.find((i) => i.ten === NHOM_DON_VI_TINH.ONG)?.id
        ),
        ten: t("baoCao.thuocTiem"),
      },
      VIEN: {
        id: JSON.stringify(
          listAllNhomDonViTinh.find((i) => i.ten === NHOM_DON_VI_TINH.VIEN)?.id
        ),
        ten: t("baoCao.thuocVienGoi"),
      },
    };
  }, [listAllNhomDonViTinh]);

  const listAllKhoTongHop = useMemo(() => {
    return uniqBy([...listAllKho, ...listKhoUser], "id");
  }, [listAllKho, listKhoUser]);

  const onHandleChange = (key, onChange, _state) => (e, data) => {
    if (key === "dsKhoaChiDinhId") {
      fetchAllPhong({
        hienThiPhongTheoKhoa: _state?.hienThiPhongTheoKhoa,
        dsKhoaId: e,
      });
      onChange(key)(e);
      setKhoaChiDinh(e, false);
      setDsKhoaId(e);
      onChange("dsPhongChiDinhId")();
    }
    if (key === "hienThiPhongTheoKhoa") {
      const checked = e?.target?.checked;
      fetchAllPhong({
        hienThiPhongTheoKhoa: checked,
        dsKhoaId: _state?.dsKhoaChiDinhId,
      });
      setCacheHienThiPhongTheoKhoa(checked, false);
      onChange(key)(checked);
    }
    if (key === "loaiDichVu") {
      getTheoTaiKhoan({
        active: true,
        page: "",
        size: "",
        dsLoaiDichVu: e,
        dsCoCheDuyetPhat: [20],
      });
      getListAllKho({
        active: true,
        page: "",
        size: "",
        dsCoCheDuyetPhat: [10, 30],
        dsLoaiDichVu: e,
      });
      onChange(key)(e);
      setCacheLoaiDichVu(e, false);
      onChange("trangThaiPhieu")(null);
      setCacheTrangThaiPhieu(null, false);
    }
    if (key === "dsPhongId") {
      onChange("thongTinCoBan")({});
      onChange("nbDotDieuTriId")(null);
      onChange("maBenhAn")(null);
      onChange("maHoSo")(null);
      onChange(key, true)(e);
      refSelect.current && refSelect.current.onClearListData();
      if (isArray(e, true) && e[0] !== "") {
        refSelect.current && refSelect.current.loadData({ [key]: e }, true);
      }
    }
    if (["maHoSo", "maBenhAn", "nbDotDieuTriId"].includes(key)) {
      onChange("thongTinCoBan")(data);
      onChange("nbDotDieuTriId")(e);
      if (key === "nbDotDieuTriId" || key === "maBenhAn") {
        onChange("maBenhAn")(data?.["maBenhAn"]);
        onChange("maHoSo")(null);
      } else if (key === "maHoSo") {
        onChange("maHoSo")(data?.["maHoSo"]);
        onChange("maBenhAn")(null);
      }
    }
    if (key === "trangThaiPhieu") {
      onChange(key)(e);
      setCacheTrangThaiPhieu(e, false);
    }
  };

  const renderInfo = (thongTinCoBan) => {
    const {
      tenNb,
      tenPhong,
      soHieuGiuong,
      maBenhAn,
      gioiTinh: _gioiTinh,
      maNb,
      maHoSo,
      ngaySinh2,
    } = thongTinCoBan || {};

    const gioiTinh =
      (listGioiTinh || []).find((item) => item.id === _gioiTinh) || {};

    return (
      <Col md={24} xl={24} xxl={24}>
        <div className="head">
          <div className="benhAn">
            <span>{tenNb}</span>{" "}
            <span className="more-info">
              {gioiTinh.ten && `- ${gioiTinh.ten} - `}
              {!gioiTinh.ten && " - "}
              {t("common.ngaySinh")}: {ngaySinh2} - {t("common.maNb")}: {maNb} -{" "}
              {t("common.maHs")}: {maHoSo} - {t("common.maBa")}: {maBenhAn} {""}
              {tenPhong ? ` - ${tenPhong}` : ""}
              {soHieuGiuong ? ` - ${soHieuGiuong}` : ""}
            </span>
          </div>
        </div>
      </Col>
    );
  };

  const renderFilter = ({ onChange, _state, onKeyDownDate }) => {
    let key;
    const listTrangThaiMemo = useMemo(() => {
      if (
        ![LOAI_DICH_VU.THUOC, LOAI_DICH_VU.CHE_PHAM_DINH_DUONG].includes(
          _state.loaiDichVu
        )
      ) {
        return (listTrangThai || []).filter((x) => x.id != 30);
      }
      return listTrangThai;
    }, [_state.loaiDichVu]);

    return (
      <Row>
        <FilterThoiGian
          t={t}
          onChange={onChange}
          _state={_state}
          onKeyDownDate={onKeyDownDate}
        />
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.khoaChiDinh")}</label>
            <Select
              onChange={onHandleChange("dsKhoaChiDinhId", onChange, _state)}
              value={
                _state.dsKhoaChiDinhId
                  ? _state.dsKhoaChiDinhId
                  : listKhoaTheoTaiKhoan.length === 1
                  ? listKhoaTheoTaiKhoan[0]?.id
                  : undefined
              }
              className="input-filter"
              placeholder={t("baoCao.chonKhoaChiDinh")}
              data={select.khoa.listKhoaTheoTaiKhoan}
              getLabel={selectMaTen}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phongChiDinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhongChiDinh")}
              onChange={onChange("dsPhongChiDinhId")}
              value={_state.dsPhongChiDinhId}
              data={listAllPhongChiDinh}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonPhong")}
              onChange={onHandleChange("dsPhongId", onChange)}
              value={_state.dsPhongId}
              data={listAllPhong}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiKho")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonLoaiKho")}
              data={(listLoaiDichVuKho || []).filter(
                (x) => x.id !== LOAI_DICH_VU.CHE_PHAM_MAU
              )}
              onChange={onHandleChange("loaiDichVu", onChange)}
              value={_state.loaiDichVu}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.kho")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonKho")}
              data={_state?.loaiDichVu ? listAllKhoTongHop : []}
              onChange={onChange("dsKhoId")}
              value={
                isArray(_state.dsKhoId, true)
                  ? _state.dsKhoId
                  : listAllKhoTongHop.length === 1
                  ? [listAllKhoTongHop[0].id]
                  : undefined
              }
              mode={"multiple"}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.doiTuongNguoiBenh")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chonDoiTuong")}
              data={listDoiTuong}
              onChange={onChange("doiTuong")}
              value={_state.doiTuong}
              hasAllOption={true}
              defaultValue=""
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("kho.loaiThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={t("pttt.chonLoaiThuoc")}
              data={Object.values(listAllNhomDonViTinhMemo)}
              onChange={onChange("nhomDonViTinhId")}
              value={_state.nhomDonViTinhId}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.phanLoaiThuoc")}</label>
            <Select
              className="input-filter"
              placeholder={t("danhMuc.chonPhanLoaiThuoc")}
              onChange={onChange("dsPhanLoaiDvKhoId", true)}
              value={_state.dsPhanLoaiDvKhoId}
              data={listAllPhanLoaiThuoc}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">
              {t("baoCao.trangThaiHangHoa")}
            </label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.trangThaiHangHoa")}
              data={listTrangThaiMemo}
              onChange={onHandleChange("trangThaiPhieu", onChange)}
              value={_state.trangThaiPhieu}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.loaiChiDinh")}</label>
            <Select
              className="input-filter"
              placeholder={t("phacDoDieuTri.chonLoaiChiDinh")}
              data={listLoaiChiDinh}
              onChange={onChange("loaiChiDinh")}
              value={_state.loaiChiDinh}
              hasAllOption={true}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.nhapMahsMaBa")} </label>
            <SelectLoadMore
              api={(params) => {
                const { value, ...searchParams } = params || {};
                let _value = (value || "").trim();

                switch (true) {
                  case testMaHoSo(_value):
                    Object.assign(searchParams, {
                      maHoSo: formatMaHoSo(_value),
                    });
                    key = "maHoSo";
                    break;
                  case /^[0-9]{7}$/.test(_value):
                    Object.assign(searchParams, { maBenhAn: _value });
                    key = "maBenhAn";
                    break;
                  default:
                    break;
                }

                if (
                  !Object.keys(searchParams).some((i) =>
                    ["maBenhAn", "maHoSo", "dsPhongId"].includes(i)
                  )
                ) {
                  return Promise.reject("Invalid search value");
                }
                return nbDotDieuTriProvider.searchNBDotDieuTriTongHop(
                  searchParams
                );
              }}
              mapData={(i) => {
                let label = i.tenNb;
                label = `${label} - ${
                  i[key === "maHoSo" ? "maHoSo" : "maBenhAn"]
                }`;
                return {
                  ...i,
                  value: i.id,
                  label,
                };
              }}
              onChange={(val, data) =>
                onHandleChange(key || "nbDotDieuTriId", onChange)(val, data)
              }
              value={_state.nbDotDieuTriId}
              keySearch={"value"}
              placeholder={t("baoCao.nhapMahsMaBa")}
              className="input-filter"
              ref={refSelect}
              firstLoadData={false}
              addParam={{
                ...(isArray(_state.dsPhongId, true) &&
                  _state.dsPhongId[0] !== "" && {
                    dsPhongId: _state.dsPhongId,
                  }),
              }}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-input">
            <label className="label-filter">{t("baoCao.soPhieuLinh")}</label>
            <InputTimeout
              className="input-filter"
              placeholder={t("quanLyNoiTru.dvNoiTru.nhapSoPhieuLinh")}
              onChange={onChange("dsSoPhieuLinh")}
              value={_state.dsSoPhieuLinh}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.doiTuongKcb")}</label>
            <Select
              onChange={onChange("dsDoiTuongKcb")}
              value={_state.dsDoiTuongKcb}
              className="input-filter"
              mode="multiple"
              placeholder={t("baoCao.chonDoiTuongKcb")}
              data={PHAN_LOAI_DOI_TUONG_KCB}
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.theoThoiGian")}</label>
            <Select
              onChange={onChange("loaiThoiGian")}
              value={_state.loaiThoiGian}
              className="input-filter select"
              placeholder={t("baoCao.chonLoaiThoiGian")}
              data={listLoaiThoiGian.filter((ltg) =>
                [20, 30].some((v) => v === ltg.id)
              )}
            />
          </div>
        </Col>

        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.loaiThongKe")}</label>
            <Select
              onChange={onChange("hoanTra")}
              value={_state.hoanTra}
              className="input-filter select"
              placeholder={t("baoCao.chonLoaiThongKe")}
              data={listLoaiThongKe}
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("common.tenThuoc")}</label>
            <SelectLoadMore
              api={dmDichVuKhoProvider.searchAll}
              mapData={(i) => ({
                value: i.id,
                label: `${i.ma}-${i.ten}`,
              })}
              onChange={onChange("dsDichVuId", true)}
              keySearch={"timKiem"}
              value={_state.dsDichVuId}
              className="input-filter"
              placeholder={t("baoCao.chonTenThuoc")}
              addParam={{
                active: true,
                dsLoaiDichVu: [LOAI_DICH_VU.THUOC],
              }}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-select">
            <label className="label-filter">{t("baoCao.chiDinhTrong")}</label>
            <Select
              className="input-filter"
              placeholder={t("baoCao.chooseChiDinhTrong")}
              data={(listLoaiDichVu || []).filter((i) =>
                [10, 20, 30, 40, 210].some((j) => j === i.id)
              )}
              onChange={onChange("dsLoaiDichVuChiDinh")}
              value={_state.dsLoaiDichVuChiDinh}
              hasAllOption={true}
              mode="multiple"
            />
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-checkbox">
            <Checkbox
              checked={_state.sapXepTheoPhong}
              onChange={(e) => {
                setCacheSapXepTheoPhong(e.target.checked, false);
                onChange("sapXepTheoPhong")(e);
              }}
            >
              {t("baoCao.sapXepDSTheoPhong")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-checkbox">
            <Checkbox
              checked={_state.sapXepTheoTenNb}
              onChange={(e) => {
                setCacheSapXepTheoTenNb(e.target.checked, false);
                onChange("sapXepTheoTenNb")(e);
              }}
            >
              {t("baoCao.sapXepTheoTenNb")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-checkbox">
            <Checkbox
              checked={_state.hienThiPhongTheoKhoa}
              onChange={onHandleChange(
                "hienThiPhongTheoKhoa",
                onChange,
                _state
              )}
            >
              {t("baoCao.hienThiPhongTheoKhoa")}
            </Checkbox>
          </div>
        </Col>
        <Col md={6} xl={6} xxl={6}>
          <div className="item-checkbox">
            <Checkbox
              checked={_state.hienThiTheoDungTich}
              onChange={onChange("hienThiTheoDungTich")}
            >
              {t("baoCao.hienThiSLTheoDungTich")}
            </Checkbox>
          </div>
        </Col>

        {/* phần thông tin cơ bản luôn nằm dưới cùng */}
        {_state.thongTinCoBan?.id && renderInfo(_state.thongTinCoBan)}
      </Row>
    );
  };

  const handleDataSearch = ({ _state }) => ({
    tuThoiGian: moment(_state.tuNgay).format("DD-MM-YYYY HH:mm:ss"),
    denThoiGian: moment(_state.denNgay).format("DD-MM-YYYY HH:mm:ss"),
    dsKhoId: isArray(_state.dsKhoId, true)
      ? _state.dsKhoId
      : listKhoUser.length === 1
      ? [listKhoUser[0].id]
      : null,
    dsPhongId: _state.dsPhongId,
    nhomDonViTinhId: _state.nhomDonViTinhId,
    dsKhoaChiDinhId: _state.dsKhoaChiDinhId
      ? _state.dsKhoaChiDinhId
      : listKhoaTheoTaiKhoan.length === 1
      ? listKhoaTheoTaiKhoan[0]?.id
      : null,
    doiTuong: _state.doiTuong,
    loaiDichVu: _state.loaiDichVu,
    trangThaiPhieu: _state.trangThaiPhieu,
    loaiChiDinh: _state.loaiChiDinh,
    maHoSo: _state.maHoSo,
    maBenhAn: _state.maBenhAn,
    sapXepTheoPhong: _state.sapXepTheoPhong,
    dsSoPhieuLinh: _state.dsSoPhieuLinh,
    dsDoiTuongKcb:
      _state.dsDoiTuongKcb && _state.dsDoiTuongKcb[0] !== ""
        ? _state.dsDoiTuongKcb.flatMap(
            (id) => getPhanLoaiDoiTuongKcb(id)?.referIds
          )
        : null,
    sapXepTheoTenNb: _state.sapXepTheoTenNb,
    dsPhanLoaiDvKhoId: _state.dsPhanLoaiDvKhoId,
    dsPhongChiDinhId: _state.dsPhongChiDinhId,
    hienThiTheoDungTich: _state.hienThiTheoDungTich,
    loaiThoiGian: _state.loaiThoiGian,
    hoanTra: _state.hoanTra,
    dsDichVuId: _state?.dsDichVuId,
    dsLoaiDichVuChiDinh: _state.dsLoaiDichVuChiDinh,
  });

  const beforeOk =
    ({ _state, _beforeOk }) =>
    () => {
      return _beforeOk();
    };

  return (
    <Main>
      <BaseBaoCao
        title={t("baoCao.k20")}
        renderFilter={renderFilter}
        getBc={getK20}
        handleDataSearch={handleDataSearch}
        beforeOk={beforeOk}
        breadcrumb={[{ title: "K20", link: "/bao-cao/k20" }]}
        initState={{
          dsPhongId: [""],
          loaiDichVu: cacheLoaiDichVu,
          dsKhoId: [""],
          loaiChiDinh: [""],
          thongTinCoBan: {},
          maHoSo: null,
          maBenhAn: null,
          nbDotDieuTriId: null,
          sapXepTheoPhong: cacheSapXepTheoPhong,
          trangThaiPhieu: cacheTrangThaiPhieu,
          dsKhoaChiDinhId:
            khoaChiDinh || listAllKhoaTheoTaiKhoan?.map((item) => item.id),
          sapXepTheoTenNb: !!cacheSapXepTheoTenNb,
          hienThiPhongTheoKhoa: cacheHienThiPhongTheoKhoa,
          dsLoaiDichVuChiDinh: [""],
        }}
      />
    </Main>
  );
};

export default K20;

import React, { useCallback } from "react";
import { useTranslation } from "react-i18next";

import { useConfirm } from "hooks";
import { <PERSON>th<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Page } from "components";
import TimKiem from "./TimKiem";
import DanhSach from "./DanhSach";
import { Main } from "./styled";
import { ROLES } from "constants/index";
import { useSearchParamsOptimized, useMutationOptimized } from "./hooks";

const DongBoChungTuXuatKhoTCKT = () => {
  const { t } = useTranslation();
  const { showConfirm } = useConfirm();

  const { searchParams, normalizedParams } = useSearchParamsOptimized();
  const { dongBoMutation, huyDongBoMutation } =
    useMutationOptimized(normalizedParams);

  const onDongBoHangLoatChungTu = useCallback(
    (e) => {
      e?.stopPropagation();
      showConfirm(
        {
          title: t("common.thongBao"),
          content: t("kho.banCoChacChanMuonDongBoChungTu"),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-confirm",
          showImg: true,
          showBtnOk: true,
        },
        () => {
          const { page, size, sort, ...dongBoParams } = searchParams;
          dongBoMutation.mutate(dongBoParams);
        }
      );
    },
    [showConfirm, t, dongBoMutation, searchParams]
  );

  const onHuyDongBoHangLoatChungTu = useCallback(
    (e) => {
      e?.stopPropagation();
      showConfirm(
        {
          title: t("common.thongBao"),
          content: t("kho.banCoChacChanMuonHuyDongBoChungTu"),
          cancelText: t("common.huy"),
          okText: t("common.dongY"),
          classNameOkText: "button-confirm",
          showImg: true,
          showBtnOk: true,
        },
        () => {
          const { page, size, sort, ...huyDongBoParams } = searchParams;
          huyDongBoMutation.mutate(huyDongBoParams);
        }
      );
    },
    [showConfirm, t, huyDongBoMutation, searchParams]
  );

  return (
    <Page
      breadcrumb={[
        { link: "/kho", title: t("kho.kho") },
        {
          link: "/kho/dong-bo-chung-tu-xuat-kho-tkct",
          title: t("kho.dongBoChungTuXuatKhoTCKT"),
        },
      ]}
      title={t("kho.dongBoChungTuXuatKhoTCKT")}
      titleRight={
        <div className="flex gap-8">
          <AuthWrapper
            accessRoles={[ROLES["KHO"].DONG_BO_CHUNG_TU_XUAT_KHO_TKCT]}
          >
            <Button type="error" onClick={onHuyDongBoHangLoatChungTu}>
              {t("kho.huyDongBoHangLoat")}
            </Button>
          </AuthWrapper>
          <AuthWrapper
            accessRoles={[ROLES["KHO"].DONG_BO_CHUNG_TU_XUAT_KHO_TKCT]}
          >
            <Button type="primary" onClick={onDongBoHangLoatChungTu}>
              {t("kho.dongBoHangLoat")}
            </Button>
          </AuthWrapper>
        </div>
      }
    >
      <Main>
        <TimKiem />
        <DanhSach
          onDongBoChungTu={dongBoMutation.mutate}
          onHuyDongBoChungTu={huyDongBoMutation.mutate}
        />
      </Main>
    </Page>
  );
};

export default DongBoChungTuXuatKhoTCKT;
